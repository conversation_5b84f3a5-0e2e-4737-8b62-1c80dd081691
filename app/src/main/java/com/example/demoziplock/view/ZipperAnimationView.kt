package com.example.demoziplock.view

import android.animation.ValueAnimator
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Typeface
import android.os.BatteryManager
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.airbnb.lottie.LottieAnimationView
import com.example.demoziplock.R
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max
import kotlin.math.min

class ZipperAnimationView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // Three LottieAnimationViews stacked on top of each other
    private lateinit var backgroundAnimationView: LottieAnimationView
    private lateinit var rowAnimationView: LottieAnimationView
    private lateinit var zipperAnimationView: LottieAnimationView

    // Touch handling variables
    private var isDragging = false
    private var startY = 0f
    private var currentProgress = 0f
    private var screenHeight = 0f

    // Animation settings
    private val maxDragPercentage = 0.8f
    private val animationDuration = 300L

    // Callback for unlock events
    private var onUnlockListener: (() -> Unit)? = null

    init {
        setupLottieAnimationViews()
    }

    private fun setupLottieAnimationViews() {
        // Create and setup background animation view
        backgroundAnimationView = LottieAnimationView(context).apply {
            setAnimation(R.raw.wallpaper_sample_file)
            progress = 0f
            pauseAnimation()
        }

        // Create and setup row animation view
        rowAnimationView = LottieAnimationView(context).apply {
            setAnimation(R.raw.row_sample_file)
            progress = 0f
            pauseAnimation()
        }

        // Create and setup zipper animation view
        zipperAnimationView = LottieAnimationView(context).apply {
            setAnimation(R.raw.zip_sample_file)
            progress = 0f
            pauseAnimation()
        }

        // Add views to FrameLayout in order: background -> row -> zipper
        val layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)

        addView(backgroundAnimationView, layoutParams)
        addView(rowAnimationView, layoutParams)
        addView(zipperAnimationView, layoutParams)

        // Set touch listener only on the top zipper view
        zipperAnimationView.setOnTouchListener { _, event ->
            onTouchEvent(event)
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        screenHeight = h.toFloat()
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isDragging = true
                startY = event.y
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    val deltaY = event.y - startY
                    val maxDragDistance = screenHeight * maxDragPercentage

                    // Calculate progress based on drag distance
                    val dragProgress = max(0f, min(1f, deltaY / maxDragDistance))
                    currentProgress = dragProgress

                    // Update all animations with the same progress
                    setAllAnimationsProgress(dragProgress)
                    invalidate()

                    // Check if fully unlocked
                    if (dragProgress >= 1f) {
                        onUnlockListener?.invoke()
                    }
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isDragging = false

                // If not fully unlocked, animate back to closed state
                if (currentProgress < 1f) {
                    animateToProgress(0f)
                }
                return true
            }
        }
        return false
    }

    private fun setAllAnimationsProgress(progress: Float) {
        backgroundAnimationView.progress = progress
        rowAnimationView.progress = progress
        zipperAnimationView.progress = progress
    }

    private fun animateToProgress(targetProgress: Float) {
        val animator = ValueAnimator.ofFloat(currentProgress, targetProgress)
        animator.duration = animationDuration
        animator.addUpdateListener { animation ->
            val progress = animation.animatedValue as Float
            currentProgress = progress
            setAllAnimationsProgress(progress)
        }
        animator.start()
    }

    fun setOnUnlockListener(listener: () -> Unit) {
        onUnlockListener = listener
    }

    fun resetToLocked() {
        currentProgress = 0f
        setAllAnimationsProgress(0f)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // Clean up LottieAnimationView resources
        backgroundAnimationView.cancelAnimation()
        rowAnimationView.cancelAnimation()
        zipperAnimationView.cancelAnimation()
    }
}
