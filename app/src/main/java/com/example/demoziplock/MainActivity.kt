package com.example.demoziplock

import android.animation.ValueAnimator
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.MotionEvent
import android.widget.Button
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.RenderMode
import com.example.demoziplock.databinding.ActivityMainBinding
import com.example.demoziplock.receiver.ScreenReceiver
import com.example.demoziplock.service.ZipperOverlayService
import kotlin.math.max
import kotlin.math.min

class MainActivity : AppCompatActivity() {

    private val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }

    private var isDragging = false
    private var startY = 0f
    private var currentProgress = 0f
    private var screenHeight = 0f

    // Maximum drag distance as percentage of screen height
    private val maxDragPercentage = 0.8f

    // Screen receiver for handling screen events
    private var screenReceiver: ScreenReceiver? = null

    // Request code for overlay permission
    private val OVERLAY_PERMISSION_REQUEST_CODE = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        //binding.zipperAnimationView.setBackgroundColor(Color.TRANSPARENT)
        //binding.zipperAnimationView.renderMode = RenderMode.SOFTWARE

        setupZipperDragEffect()
        setupOverlayService()
        checkOverlayPermission()
        setupTestButtons()
    }

    private fun setupZipperDragEffect() {
        // Get screen height
        binding.root.post {
            screenHeight = binding.root.height.toFloat()
        }

        // Set up touch listener on the zipper animation view
        /*binding.zipperAnimationView.setOnTouchListener { view, event ->
            handleZipperTouch(event)
        }

        // Initialize all animations to paused state
        binding.backgroundAnimationView.pauseAnimation()
        binding.rowAnimationView.pauseAnimation()
        binding.zipperAnimationView.pauseAnimation()*/

        // Set initial progress to 0
        setAllAnimationsProgress(0f)
    }

    private fun handleZipperTouch(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isDragging = true
                startY = event.y
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    val deltaY = event.y - startY
                    val maxDragDistance = screenHeight * maxDragPercentage

                    // Calculate progress based on drag distance
                    val dragProgress = max(0f, min(1f, deltaY / maxDragDistance))
                    currentProgress = dragProgress

                    // Update all animations with the same progress
                    setAllAnimationsProgress(dragProgress)
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isDragging = false

                // Animate back to closed state (progress 0) with smooth transition
                animateToProgress(0f)
                return true
            }
        }
        return false
    }

    private fun setAllAnimationsProgress(progress: Float) {
        /*binding.backgroundAnimationView.progress = progress
        binding.rowAnimationView.progress = progress
        binding.zipperAnimationView.progress = progress*/
    }

    private fun animateToProgress(targetProgress: Float) {
        val animator = ValueAnimator.ofFloat(currentProgress, targetProgress)
        animator.duration = 300 // 300ms animation
        animator.addUpdateListener { animation ->
            val progress = animation.animatedValue as Float
            currentProgress = progress
            setAllAnimationsProgress(progress)
        }
        animator.start()
    }

    private fun setupTestButtons() {
        binding.btnTestOverlay.setOnClickListener {
            val intent = Intent(this, ZipperOverlayService::class.java).apply {
                action = ZipperOverlayService.ACTION_SHOW_OVERLAY
            }
            startService(intent)
        }

        binding.btnHideOverlay.setOnClickListener {
            val intent = Intent(this, ZipperOverlayService::class.java).apply {
                action = ZipperOverlayService.ACTION_HIDE_OVERLAY
            }
            startService(intent)
        }
    }

    private fun setupOverlayService() {
        // Register screen receiver
        screenReceiver = ScreenReceiver()
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)
        }
        registerReceiver(screenReceiver, intentFilter)
    }

    private fun checkOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Toast.makeText(this, "Overlay permission is required for screen lock", Toast.LENGTH_LONG).show()
                val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = Uri.parse("package:$packageName")
                }
                startActivityForResult(intent, OVERLAY_PERMISSION_REQUEST_CODE)
            } else {
                Toast.makeText(this, "Overlay permission granted. Screen lock is ready!", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == OVERLAY_PERMISSION_REQUEST_CODE) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (Settings.canDrawOverlays(this)) {
                    Toast.makeText(this, "Overlay permission granted!", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "Overlay permission denied. Screen lock won't work.", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Unregister screen receiver
        screenReceiver?.let {
            unregisterReceiver(it)
        }
    }
}